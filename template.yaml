AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'HopieApp - Complete Serverless Application for Couples'

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Deployment stage

  CognitoDomainPrefix:
    Type: String
    Default: hopie-app
    Description: Cognito domain prefix

  EnableLambdaFunctions:
    Type: String
    Default: 'true'
    AllowedValues: ['true', 'false']
    Description: Enable Lambda functions deployment (set to false to skip Lambda deployment)

  EnableWebSocket:
    Type: String
    Default: 'true'
    AllowedValues: ['true', 'false']
    Description: Enable WebSocket API deployment

  GoogleClientId:
    Type: String
    Default: ''
    Description: Google OAuth Client ID (get from Google Cloud Console)
    NoEcho: false

  GoogleClientSecret:
    Type: String
    Default: ''
    Description: Google OAuth Client Secret (get from Google Cloud Console)
    NoEcho: true

Conditions:
  DeployLambdas: !Equals [!Ref EnableLambdaFunctions, 'true']
  DeployWebSocket: !Equals [!Ref EnableWebSocket, 'true']
  IsProd: !Equals [!Ref Stage, 'prod']
  HasGoogleAuth: !And
    - !Not [!Equals [!Ref GoogleClientId, '']]
    - !Not [!Equals [!Ref GoogleClientSecret, '']]

Globals:
  Function:
    Runtime: nodejs18.x
    MemorySize: 512
    Timeout: 30
    Environment:
      Variables:
        STAGE: !Ref Stage
        DYNAMODB_TABLE: !Ref HopieMainTable
        USER_POOL_ID: !Ref CognitoUserPool
        USER_POOL_CLIENT_ID: !Ref CognitoUserPoolClient
        USER_CONTENT_BUCKET: !Ref UserContentBucket
        APP_ASSETS_BUCKET: !Ref AppAssetsBucket
        CLOUDFRONT_DOMAIN: !GetAtt CloudFrontDistribution.DomainName

Resources:
  # ===== COGNITO USER POOL WITH GOOGLE AUTH =====
  CognitoUserPool:
    Type: AWS::Cognito::UserPool
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      UserPoolName: !Sub 'hopie-users-${Stage}'
      AliasAttributes:
        - email
        - preferred_username
      AutoVerifiedAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: name
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: given_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: family_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: picture
          AttributeDataType: String
          Required: false
          Mutable: true
      UserPoolAddOns:
        AdvancedSecurityMode: ENFORCED

  # Google Identity Provider
  GoogleIdentityProvider:
    Type: AWS::Cognito::UserPoolIdentityProvider
    Condition: HasGoogleAuth
    DependsOn: CognitoUserPool
    Properties:
      UserPoolId: !Ref CognitoUserPool
      ProviderName: Google
      ProviderType: Google
      ProviderDetails:
        client_id: !Ref GoogleClientId
        client_secret: !Ref GoogleClientSecret
        authorize_scopes: 'email openid profile'
      AttributeMapping:
        email: email
        name: name
        given_name: given_name
        family_name: family_name
        picture: picture
        username: sub

  CognitoUserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    DependsOn:
      - CognitoUserPool
      - GoogleIdentityProvider
    Properties:
      UserPoolId: !Ref CognitoUserPool
      ClientName: !Sub 'hopie-client-${Stage}'
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_USER_SRP_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      SupportedIdentityProviders:
        - COGNITO
        - !If [HasGoogleAuth, Google, !Ref 'AWS::NoValue']
      CallbackURLs:
        - !Sub 'https://${CognitoDomainPrefix}-${Stage}.auth.${AWS::Region}.amazoncognito.com/oauth2/idpresponse'
        - 'http://localhost:3000/auth/callback'
        - 'https://localhost:3000/auth/callback'
      LogoutURLs:
        - !Sub 'https://${CognitoDomainPrefix}-${Stage}.auth.${AWS::Region}.amazoncognito.com/logout'
        - 'http://localhost:3000/auth/logout'
        - 'https://localhost:3000/auth/logout'
      AllowedOAuthFlows:
        - code
        - implicit
      AllowedOAuthScopes:
        - email
        - openid
        - profile
      AllowedOAuthFlowsUserPoolClient: true

  CognitoUserPoolDomain:
    Type: AWS::Cognito::UserPoolDomain
    DependsOn: CognitoUserPool
    Properties:
      Domain: !Sub '${CognitoDomainPrefix}-${Stage}'
      UserPoolId: !Ref CognitoUserPool

  # ===== DYNAMODB TABLE =====
  HopieMainTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub 'HopieApp-${Stage}'
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
        - AttributeName: GSI1PK
          AttributeType: S
        - AttributeName: GSI1SK
          AttributeType: S
        - AttributeName: GSI2PK
          AttributeType: S
        - AttributeName: GSI2SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: GSI1
          KeySchema:
            - AttributeName: GSI1PK
              KeyType: HASH
            - AttributeName: GSI1SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: GSI2
          KeySchema:
            - AttributeName: GSI2PK
              KeyType: HASH
            - AttributeName: GSI2SK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  # ===== S3 BUCKETS =====
  UserContentBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub 'hopie-user-content-${Stage}-${AWS::AccountId}'
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders: ['*']
            AllowedMethods: [GET, PUT, POST, DELETE]
            AllowedOrigins: ['*']
            MaxAge: 3000
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  AppAssetsBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub 'hopie-app-assets-${Stage}-${AWS::AccountId}'
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders: ['*']
            AllowedMethods: [GET]
            AllowedOrigins: ['*']
            MaxAge: 86400
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # ===== CLOUDFRONT DISTRIBUTION =====
  CloudFrontOriginAccessIdentity:
    Type: AWS::CloudFront::CloudFrontOriginAccessIdentity
    Properties:
      CloudFrontOriginAccessIdentityConfig:
        Comment: !Sub 'OAI for HopieApp ${Stage}'

  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    DependsOn:
      - CloudFrontOriginAccessIdentity
      - UserContentBucket
      - AppAssetsBucket
    Properties:
      DistributionConfig:
        Comment: !Sub 'HopieApp CDN ${Stage}'
        DefaultRootObject: index.html
        Enabled: true
        HttpVersion: http2
        PriceClass: PriceClass_100
        Origins:
          - Id: UserContentOrigin
            DomainName: !GetAtt UserContentBucket.RegionalDomainName
            S3OriginConfig:
              OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${CloudFrontOriginAccessIdentity}'
          - Id: AppAssetsOrigin
            DomainName: !GetAtt AppAssetsBucket.RegionalDomainName
            S3OriginConfig:
              OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${CloudFrontOriginAccessIdentity}'
        DefaultCacheBehavior:
          TargetOriginId: AppAssetsOrigin
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad  # CachingDisabled
          OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf  # CORS-S3Origin
        CacheBehaviors:
          - PathPattern: '/user-content/*'
            TargetOriginId: UserContentOrigin
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf

  # ===== BUCKET POLICIES =====
  UserContentBucketPolicy:
    Type: AWS::S3::BucketPolicy
    DependsOn:
      - UserContentBucket
      - CloudFrontOriginAccessIdentity
    Properties:
      Bucket: !Ref UserContentBucket
      PolicyDocument:
        Statement:
          - Sid: AllowCloudFrontAccess
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${CloudFrontOriginAccessIdentity}'
            Action: 's3:GetObject'
            Resource: !Sub 'arn:aws:s3:::${UserContentBucket}/*'

  AppAssetsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    DependsOn:
      - AppAssetsBucket
      - CloudFrontOriginAccessIdentity
    Properties:
      Bucket: !Ref AppAssetsBucket
      PolicyDocument:
        Statement:
          - Sid: AllowCloudFrontAccess
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${CloudFrontOriginAccessIdentity}'
            Action: 's3:GetObject'
            Resource: !Sub 'arn:aws:s3:::${AppAssetsBucket}/*'

  # ===== API GATEWAY =====
  HopieApi:
    Type: AWS::Serverless::Api
    DependsOn: CognitoUserPool
    Properties:
      Name: !Sub 'hopie-api-${Stage}'
      StageName: !Ref Stage
      Cors:
        AllowMethods: "'*'"
        AllowHeaders: "'*'"
        AllowOrigin: "'*'"
      Auth:
        DefaultAuthorizer: CognitoAuthorizer
        Authorizers:
          CognitoAuthorizer:
            UserPoolArn: !GetAtt CognitoUserPool.Arn

  # ===== WEBSOCKET API =====
  WebSocketApi:
    Type: AWS::ApiGatewayV2::Api
    Condition: DeployWebSocket
    Properties:
      Name: !Sub 'hopie-websocket-${Stage}'
      ProtocolType: WEBSOCKET
      RouteSelectionExpression: $request.body.action

  WebSocketStage:
    Type: AWS::ApiGatewayV2::Stage
    Condition: DeployWebSocket
    DependsOn: WebSocketApi
    Properties:
      ApiId: !Ref WebSocketApi
      StageName: !Ref Stage
      AutoDeploy: true

  # WebSocket Routes
  ConnectRoute:
    Type: AWS::ApiGatewayV2::Route
    Condition: DeployWebSocket
    DependsOn:
      - WebSocketApi
      - ConnectIntegration
    Properties:
      ApiId: !Ref WebSocketApi
      RouteKey: $connect
      Target: !Sub 'integrations/${ConnectIntegration}'

  DisconnectRoute:
    Type: AWS::ApiGatewayV2::Route
    Condition: DeployWebSocket
    DependsOn:
      - WebSocketApi
      - DisconnectIntegration
    Properties:
      ApiId: !Ref WebSocketApi
      RouteKey: $disconnect
      Target: !Sub 'integrations/${DisconnectIntegration}'

  DefaultRoute:
    Type: AWS::ApiGatewayV2::Route
    Condition: DeployWebSocket
    DependsOn:
      - WebSocketApi
      - DefaultIntegration
    Properties:
      ApiId: !Ref WebSocketApi
      RouteKey: $default
      Target: !Sub 'integrations/${DefaultIntegration}'

  # WebSocket Integrations
  ConnectIntegration:
    Type: AWS::ApiGatewayV2::Integration
    Condition: DeployWebSocket
    DependsOn:
      - WebSocketApi
      - ChatFunction
    Properties:
      ApiId: !Ref WebSocketApi
      IntegrationType: AWS_PROXY
      IntegrationUri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ChatFunction.Arn}/invocations'

  DisconnectIntegration:
    Type: AWS::ApiGatewayV2::Integration
    Condition: DeployWebSocket
    DependsOn:
      - WebSocketApi
      - ChatFunction
    Properties:
      ApiId: !Ref WebSocketApi
      IntegrationType: AWS_PROXY
      IntegrationUri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ChatFunction.Arn}/invocations'

  DefaultIntegration:
    Type: AWS::ApiGatewayV2::Integration
    Condition: DeployWebSocket
    DependsOn:
      - WebSocketApi
      - ChatFunction
    Properties:
      ApiId: !Ref WebSocketApi
      IntegrationType: AWS_PROXY
      IntegrationUri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ChatFunction.Arn}/invocations'

  # WebSocket Lambda Permissions
  ChatFunctionConnectPermission:
    Type: AWS::Lambda::Permission
    Condition: DeployWebSocket
    DependsOn:
      - ChatFunction
      - WebSocketApi
    Properties:
      FunctionName: !Ref ChatFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${WebSocketApi}/*'

  # ===== LAMBDA FUNCTIONS =====
  # Auth Service
  AuthFunction:
    Type: AWS::Serverless::Function
    Condition: DeployLambdas
    DependsOn:
      - HopieMainTable
      - HopieApi
      - CognitoUserPool
    Properties:
      FunctionName: !Sub 'hopie-auth-${Stage}'
      CodeUri: src/auth/
      Handler: index.handler

      Events:
        AuthApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /auth/{proxy+}
            Method: ANY
            Auth:
              Authorizer: NONE
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:*
              Resource: !GetAtt CognitoUserPool.Arn



  # User Service
  UserFunction:
    Type: AWS::Serverless::Function
    Condition: DeployLambdas
    DependsOn:
      - HopieMainTable
      - HopieApi
    Properties:
      FunctionName: !Sub 'hopie-user-${Stage}'
      CodeUri: src/user/
      Handler: index.handler

      Events:
        UserApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /users/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable



  # Couple Service
  CoupleFunction:
    Type: AWS::Serverless::Function
    Condition: DeployLambdas
    DependsOn:
      - HopieMainTable
      - HopieApi
    Properties:
      FunctionName: !Sub 'hopie-couple-${Stage}'
      CodeUri: src/couple/
      Handler: index.handler
      Events:
        CoupleApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /couples/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Tree Service
  TreeFunction:
    Type: AWS::Serverless::Function
    Condition: DeployLambdas
    DependsOn:
      - HopieMainTable
      - HopieApi
    Properties:
      FunctionName: !Sub 'hopie-tree-${Stage}'
      CodeUri: src/tree/
      Handler: index.handler
      Events:
        TreeApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /trees/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Questions Service
  QuestionsFunction:
    Type: AWS::Serverless::Function
    Condition: DeployLambdas
    DependsOn:
      - HopieMainTable
      - HopieApi
    Properties:
      FunctionName: !Sub 'hopie-questions-${Stage}'
      CodeUri: src/questions/
      Handler: index.handler
      Events:
        QuestionsApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /questions/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Life Plan Service
  LifePlanFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-lifeplan-${Stage}'
      CodeUri: src/lifeplan/
      Handler: index.handler
      Events:
        LifePlanApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /lifeplan/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Places Service
  PlacesFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-places-${Stage}'
      CodeUri: src/places/
      Handler: index.handler
      Events:
        PlacesApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /places/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Chat Service (WebSocket)
  ChatFunction:
    Type: AWS::Serverless::Function
    Condition: DeployLambdas
    DependsOn:
      - HopieMainTable
    Properties:
      FunctionName: !Sub 'hopie-chat-${Stage}'
      CodeUri: src/chat/
      Handler: index.handler

      Environment:
        Variables:
          WEBSOCKET_ENDPOINT: !Sub 'https://${WebSocketApi}.execute-api.${AWS::Region}.amazonaws.com/${Stage}'
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - execute-api:ManageConnections
              Resource: !Sub 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${WebSocketApi}/*'



  # Location Service
  LocationFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-location-${Stage}'
      CodeUri: src/location/
      Handler: index.handler
      Events:
        LocationApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /location/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Stats Service
  StatsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-stats-${Stage}'
      CodeUri: src/stats/
      Handler: index.handler
      Events:
        StatsApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /stats/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # Image Service
  ImageFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-image-${Stage}'
      CodeUri: src/image/
      Handler: index.handler
      Events:
        ImageApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /images/{proxy+}
            Method: ANY
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable
        - S3CrudPolicy:
            BucketName: !Ref UserContentBucket
        - S3CrudPolicy:
            BucketName: !Ref AppAssetsBucket

  # Image Processor (S3 Trigger)
  ImageProcessorFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-image-processor-${Stage}'
      CodeUri: src/image-processor/
      Handler: index.handler
      MemorySize: 1024
      Timeout: 60
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable
        - S3CrudPolicy:
            BucketName: !Ref UserContentBucket
        - S3CrudPolicy:
            BucketName: !Ref AppAssetsBucket

  # Notification Service
  NotificationFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-notification-${Stage}'
      CodeUri: src/notification/
      Handler: index.handler
      Events:
        NotificationApi:
          Type: Api
          Properties:
            RestApiId: !Ref HopieApi
            Path: /notifications/{proxy+}
            Method: ANY
        DynamoDBStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt HopieMainTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable
        - DynamoDBStreamReadPolicy:
            TableName: !Ref HopieMainTable
            StreamName: !Select [3, !Split ['/', !GetAtt HopieMainTable.StreamArn]]

  # Scheduler Service (EventBridge)
  SchedulerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'hopie-scheduler-${Stage}'
      CodeUri: src/scheduler/
      Handler: index.handler
      Events:
        DailyQuestionSchedule:
          Type: Schedule
          Properties:
            Schedule: cron(0 0 * * ? *)  # Daily at midnight UTC
            Input: '{"action": "createDailyQuestion"}'
        StreakCalculationSchedule:
          Type: Schedule
          Properties:
            Schedule: cron(0 1 * * ? *)  # Daily at 1 AM UTC
            Input: '{"action": "calculateStreaks"}'
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref HopieMainTable

  # ===== PERMISSIONS =====
  # ImageProcessorInvokePermission:
  #   Type: AWS::Lambda::Permission
  #   Properties:
  #     FunctionName: !Ref ImageProcessorFunction
  #     Action: lambda:InvokeFunction
  #     Principal: s3.amazonaws.com
  #     SourceAccount: !Ref AWS::AccountId
  #     SourceArn: !Sub 'arn:aws:s3:::${UserContentBucket}/*'

Outputs:
  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !Sub 'https://${HopieApi}.execute-api.${AWS::Region}.amazonaws.com/${Stage}'
    Export:
      Name: !Sub '${AWS::StackName}-ApiUrl'

  WebSocketUrl:
    Condition: DeployWebSocket
    Description: WebSocket API URL
    Value: !Sub 'wss://${WebSocketApi}.execute-api.${AWS::Region}.amazonaws.com/${Stage}'
    Export:
      Name: !Sub '${AWS::StackName}-WebSocketUrl'

  CloudFrontUrl:
    Description: CloudFront Distribution URL
    Value: !Sub 'https://${CloudFrontDistribution.DomainName}'
    Export:
      Name: !Sub '${AWS::StackName}-CloudFrontUrl'

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref CognitoUserPool
    Export:
      Name: !Sub '${AWS::StackName}-UserPoolId'

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref CognitoUserPoolClient
    Export:
      Name: !Sub '${AWS::StackName}-UserPoolClientId'

  DynamoDBTable:
    Description: DynamoDB Table Name
    Value: !Ref HopieMainTable
    Export:
      Name: !Sub '${AWS::StackName}-DynamoDBTable'

  UserContentBucket:
    Description: User Content S3 Bucket
    Value: !Ref UserContentBucket
    Export:
      Name: !Sub '${AWS::StackName}-UserContentBucket'

  AppAssetsBucket:
    Description: App Assets S3 Bucket
    Value: !Ref AppAssetsBucket
    Export:
      Name: !Sub '${AWS::StackName}-AppAssetsBucket'
